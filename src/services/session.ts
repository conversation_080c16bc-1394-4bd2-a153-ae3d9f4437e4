import { UserSession, UserSessions } from "../types/session";

// Simple in-memory session store for pending orders
export const userSessions: UserSessions = new Map();

export const setUserSession = (userId: string, session: UserSession): void => {
  userSessions.set(userId, session);
};

export const getUserSession = (userId: string): UserSession | undefined => {
  return userSessions.get(userId);
};

export const clearUserSession = (userId: string): void => {
  userSessions.delete(userId);
};

export const updateUserSession = (
  userId: string,
  updates: Partial<UserSession>
): void => {
  const currentSession = userSessions.get(userId) || {};
  userSessions.set(userId, { ...currentSession, ...updates });
};

export const clearSessionProperty = (
  userId: string,
  property: keyof UserSession
): void => {
  const session = userSessions.get(userId);
  if (session) {
    delete session[property];
    if (Object.keys(session).length === 0) {
      userSessions.delete(userId);
    } else {
      userSessions.set(userId, session);
    }
  }
};
