import bot from "./bot";
import dotenv from "dotenv";

// Load environment variables
dotenv.config();

const PORT = process.env.PORT ?? 3001;
const NODE_ENV = process.env.NODE_ENV ?? "development";
const WEBHOOK_URL = process.env.WEBHOOK_URL;

async function startBot() {
  try {
    console.log("🤖 Starting Marketplace Bot...");

    // Get bot info first
    const botInfo = await bot.telegram.getMe();
    console.log(`✅ Bot @${botInfo.username} is ready`);

    if (NODE_ENV === "production" && WEBHOOK_URL) {
      // Production mode with webhook
      console.log("🌐 Setting up webhook for production...");
      await bot.telegram.setWebhook(`${WEBHOOK_URL}/webhook`);

      // Start webhook server
      bot.webhookCallback("/webhook");
      console.log(`🚀 Bot webhook server started on port ${PORT}`);
    } else {
      // Development mode with polling
      console.log("🔄 Starting bot in polling mode (development)...");

      // Remove webhook if it exists and force stop any existing polling
      try {
        await bot.telegram.deleteWebhook({ drop_pending_updates: true });
        console.log("✅ Webhook removed and pending updates dropped");
      } catch (error) {
        console.log(
          "ℹ️ No webhook to remove or error removing webhook:",
          error instanceof Error ? error.message : String(error)
        );
      }

      // Start polling with dropPendingUpdates to avoid conflicts
      bot
        .launch({ dropPendingUpdates: true })
        .then(() => {
          console.log("🚀 Bot started successfully in polling mode");
        })
        .catch((error) => {
          console.error("❌ Failed to launch bot:", error);
          if (
            error.message?.includes("409") ||
            error.message?.includes("Conflict")
          ) {
            console.log(
              "💡 Tip: Another bot instance might be running. Please stop it first."
            );
            console.log("💡 You can also wait a few seconds and try again.");
          }
          process.exit(1);
        });
    }

    // Set up global menu button (no chat_id needed for global setting)
    console.log("🔧 Setting up global menu button...");
    await bot.telegram.setChatMenuButton({
      menuButton: {
        type: "web_app",
        text: "Open Marketplace",
        web_app: {
          url:
            process.env.WEB_APP_URL ??
            "https://4d5rqhd0-3000.euw.devtunnels.ms/",
        },
      },
    });
    console.log("✅ Global menu button configured");

    // Set up bot commands for better UX
    console.log("🔧 Setting up bot commands...");
    await bot.telegram.setMyCommands([
      { command: "start", description: "Start the bot and show main menu" },
      { command: "help", description: "Show help information" },
    ]);
    console.log("✅ Bot commands configured");

    // Set bot description to enable the "Open" button in chat list
    console.log("🔧 Setting up bot description...");
    await bot.telegram.setMyDescription(
      "🛍️ Marketplace Bot - Your gateway to the marketplace platform. Use the menu button or commands to get started!"
    );
    await bot.telegram.setMyShortDescription(
      "🛍️ Access the marketplace platform"
    );
    console.log("✅ Bot description configured");
  } catch (error) {
    console.error("❌ Failed to start bot:", error);
    process.exit(1);
  }
}

// Graceful shutdown
process.once("SIGINT", () => {
  console.log("🛑 Received SIGINT, shutting down gracefully...");
  bot.stop("SIGINT");
  process.exit(0);
});

process.once("SIGTERM", () => {
  console.log("🛑 Received SIGTERM, shutting down gracefully...");
  bot.stop("SIGTERM");
  process.exit(0);
});

// Handle uncaught exceptions
process.on("uncaughtException", (error) => {
  console.error("💥 Uncaught Exception:", error);
  process.exit(1);
});

process.on("unhandledRejection", (reason, promise) => {
  console.error("💥 Unhandled Rejection at:", promise, "reason:", reason);
  process.exit(1);
});

// Start the bot
startBot();
