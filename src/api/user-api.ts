import {
  doc,
  updateDoc,
  getDoc,
  setDoc,
  deleteDoc,
  collection,
  query,
  where,
  getDocs,
  orderBy,
  limit,
  Timestamp
} from 'firebase/firestore';
import { httpsCallable } from 'firebase/functions';
import { firestore, firebaseFunctions } from '@/root-context';
import { UserEntity, UserBalance } from '@/core.constants';

export interface UpdateUserData {
  name?: string;
  displayName?: string;
  email?: string;
  photoURL?: string;
  tg_id?: string;
  ton_wallet_address?: string;
  raw_ton_wallet_address?: string;
  referral_id?: string;
  balance?: UserBalance;
  role?: "admin" | "user";
}

export interface CreateUserData extends UpdateUserData {
  id: string;
}

interface CloudFunctionResponse {
  success: boolean;
  message: string;
  updatedFields: string[];
}

/**
 * Update user data using changeUserData cloud function
 * Supports: displayName (as name), tg_id, ton_wallet_address, raw_ton_wallet_address, referral_id
 * For other fields, use updateUserDirect()
 */
export const updateUser = async (
  userId: string,
  updateData: UpdateUserData
): Promise<UserEntity> => {
  try {
    console.log('updateUser called with:', { userId, updateData });

    // Check if user is authenticated
    const { firebaseAuth } = await import('@/root-context');
    const currentAuthUser = firebaseAuth.currentUser;
    console.log('Current Firebase Auth user:', currentAuthUser?.uid);

    if (!currentAuthUser) {
      throw new Error('User not authenticated with Firebase Auth');
    }

    // Create the cloud function callable
    const changeUserData = httpsCallable(firebaseFunctions, 'changeUserData');

    // Map UpdateUserData to cloud function parameters
    const cloudFunctionData: any = {
      userId: userId // Add the userId parameter
    };

    if (updateData.displayName !== undefined) {
      cloudFunctionData.name = updateData.displayName;
    }
    if (updateData.tg_id !== undefined) {
      cloudFunctionData.tg_id = updateData.tg_id;
    }
    if (updateData.ton_wallet_address !== undefined) {
      cloudFunctionData.ton_wallet_address = updateData.ton_wallet_address;
    }
    if (updateData.raw_ton_wallet_address !== undefined) {
      cloudFunctionData.raw_ton_wallet_address = updateData.raw_ton_wallet_address;
    }
    if (updateData.referral_id !== undefined) {
      cloudFunctionData.referral_id = updateData.referral_id;
    }

    // Call the cloud function
    console.log('Calling cloud function with data:', cloudFunctionData);
    const result = await changeUserData(cloudFunctionData);
    console.log('Cloud function result:', result);
    const response = result.data as CloudFunctionResponse;

    if (!response.success) {
      throw new Error(response.message || 'Failed to update user');
    }

    // Get updated user data
    const updatedUser = await getUserById(userId);
    if (!updatedUser) {
      throw new Error(`User with ID ${userId} not found after update`);
    }

    return updatedUser;
  } catch (error) {
    console.error(`Error updating user ${userId}:`, error);
    throw error;
  }
};

/**
 * Update user data directly in Firestore (for fields not supported by cloud function)
 */
export const updateUserDirect = async (
  userId: string,
  updateData: UpdateUserData
): Promise<UserEntity> => {
  try {
    const userRef = doc(firestore, 'users', userId);

    // Get current user data first
    const userDoc = await getDoc(userRef);
    if (!userDoc.exists()) {
      throw new Error(`User with ID ${userId} not found`);
    }

    // Prepare update data with timestamp
    const dataToUpdate = {
      ...updateData,
      updatedAt: Timestamp.now()
    };

    // Remove undefined values
    Object.keys(dataToUpdate).forEach(key => {
      if (dataToUpdate[key as keyof typeof dataToUpdate] === undefined) {
        delete dataToUpdate[key as keyof typeof dataToUpdate];
      }
    });

    await updateDoc(userRef, dataToUpdate);

    // Return updated user data
    const updatedDoc = await getDoc(userRef);
    return {
      id: updatedDoc.id,
      ...updatedDoc.data()
    } as UserEntity;
  } catch (error) {
    console.error(`Error updating user ${userId} directly:`, error);
    throw error;
  }
};

/**
 * Create a new user in Firebase users collection
 */
export const createUser = async (userData: CreateUserData): Promise<UserEntity> => {
  try {
    const userRef = doc(firestore, 'users', userData.id);
    
    const newUser: UserEntity = {
      ...userData,
      role: userData.role || 'user',
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    } as UserEntity;

    await setDoc(userRef, newUser);
    
    return newUser;
  } catch (error) {
    console.error('Error creating user:', error);
    throw error;
  }
};

/**
 * Get user by ID
 */
export const getUserById = async (userId: string): Promise<UserEntity | null> => {
  try {
    const userRef = doc(firestore, 'users', userId);
    const userDoc = await getDoc(userRef);
    
    if (!userDoc.exists()) {
      return null;
    }
    
    return {
      id: userDoc.id,
      ...userDoc.data()
    } as UserEntity;
  } catch (error) {
    console.error(`Error getting user ${userId}:`, error);
    throw error;
  }
};

/**
 * Get user by Telegram ID
 */
export const getUserByTelegramId = async (tgId: string): Promise<UserEntity | null> => {
  try {
    const usersRef = collection(firestore, 'users');
    const q = query(usersRef, where('tg_id', '==', tgId), limit(1));
    const querySnapshot = await getDocs(q);
    
    if (querySnapshot.empty) {
      return null;
    }
    
    const userDoc = querySnapshot.docs[0];
    return {
      id: userDoc.id,
      ...userDoc.data()
    } as UserEntity;
  } catch (error) {
    console.error(`Error getting user by Telegram ID ${tgId}:`, error);
    throw error;
  }
};

/**
 * Get user by TON wallet address
 */
export const getUserByWalletAddress = async (walletAddress: string): Promise<UserEntity | null> => {
  try {
    const usersRef = collection(firestore, 'users');
    const q = query(
      usersRef, 
      where('ton_wallet_address', '==', walletAddress), 
      limit(1)
    );
    const querySnapshot = await getDocs(q);
    
    if (querySnapshot.empty) {
      return null;
    }
    
    const userDoc = querySnapshot.docs[0];
    return {
      id: userDoc.id,
      ...userDoc.data()
    } as UserEntity;
  } catch (error) {
    console.error(`Error getting user by wallet address ${walletAddress}:`, error);
    throw error;
  }
};

/**
 * Update user balance (uses direct Firestore update as balance is not supported by cloud function)
 */
export const updateUserBalance = async (
  userId: string,
  balance: UserBalance
): Promise<UserEntity> => {
  return updateUserDirect(userId, { balance });
};

/**
 * Update user wallet addresses (saves both tonWalletAddress and rawTonWalletAddress)
 * Example:
 * - tonWalletAddress: UQBzm-T_1XfNZKkKA0CtkkTu9E2uEGt8le7oZ1k1p1CW5LV1
 * - rawTonWalletAddress: 0:739be4ffd577cd64a90a0340ad9244eef44dae106b7c95eee8675935a75096e4
 */
export const updateUserWallet = async (
  userId: string,
  tonWalletAddress: string,
  rawTonWalletAddress?: string
): Promise<UserEntity> => {
  try {
    // Use cloud function for both wallet addresses
    const updateData: UpdateUserData = {
      ton_wallet_address: tonWalletAddress
    };

    if (rawTonWalletAddress !== undefined) {
      updateData.raw_ton_wallet_address = rawTonWalletAddress;
    }

    return await updateUser(userId, updateData);
  } catch (error) {
    console.error(`Error updating wallet for user ${userId}:`, error);
    throw error;
  }
};

/**
 * Update user Telegram data (uses cloud function for tg_id and displayName, direct update for photoURL)
 */
export const updateUserTelegram = async (
  userId: string,
  tgId: string,
  displayName?: string,
  photoURL?: string
): Promise<UserEntity> => {
  const cloudFunctionData: UpdateUserData = { tg_id: tgId };

  if (displayName) cloudFunctionData.displayName = displayName;

  // Use cloud function for tg_id and displayName
  await updateUser(userId, cloudFunctionData);

  // Use direct update for photoURL if provided (not supported by cloud function)
  if (photoURL !== undefined) {
    return updateUserDirect(userId, { photoURL });
  }

  // Get updated user data
  const updatedUser = await getUserById(userId);
  if (!updatedUser) {
    throw new Error(`User with ID ${userId} not found after Telegram update`);
  }

  return updatedUser;
};

/**
 * Set user referral
 */
export const setUserReferral = async (
  userId: string, 
  referralId: string
): Promise<UserEntity> => {
  // First check if user already has a referral
  const currentUser = await getUserById(userId);
  if (currentUser?.referral_id) {
    throw new Error('User already has a referral ID set');
  }
  
  return updateUser(userId, { referral_id: referralId });
};

/**
 * Get all users with pagination
 */
export const getUsers = async (
  limitCount: number = 50,
  orderByField: string = 'createdAt'
): Promise<UserEntity[]> => {
  try {
    const usersRef = collection(firestore, 'users');
    const q = query(
      usersRef, 
      orderBy(orderByField as string, 'desc'), 
      limit(limitCount)
    );
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as UserEntity[];
  } catch (error) {
    console.error('Error getting users:', error);
    throw error;
  }
};

/**
 * Delete user (admin only)
 */
export const deleteUser = async (userId: string): Promise<void> => {
  try {
    const userRef = doc(firestore, 'users', userId);
    await deleteDoc(userRef);
  } catch (error) {
    console.error(`Error deleting user ${userId}:`, error);
    throw error;
  }
};

/**
 * Get users by role
 */
export const getUsersByRole = async (role: "admin" | "user"): Promise<UserEntity[]> => {
  try {
    const usersRef = collection(firestore, 'users');
    const q = query(usersRef, where('role', '==', role));
    const querySnapshot = await getDocs(q);

    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as UserEntity[];
  } catch (error) {
    console.error(`Error getting users by role ${role}:`, error);
    throw error;
  }
};

/**
 * Check if user exists
 */
export const userExists = async (userId: string): Promise<boolean> => {
  try {
    const userRef = doc(firestore, 'users', userId);
    const userDoc = await getDoc(userRef);
    return userDoc.exists();
  } catch (error) {
    console.error(`Error checking if user ${userId} exists:`, error);
    return false;
  }
};

/**
 * Batch update multiple users
 */
export const batchUpdateUsers = async (
  updates: Array<{ userId: string; data: UpdateUserData }>
): Promise<UserEntity[]> => {
  try {
    const results: UserEntity[] = [];

    // Process updates sequentially to avoid overwhelming Firestore
    for (const update of updates) {
      const updatedUser = await updateUser(update.userId, update.data);
      results.push(updatedUser);
    }

    return results;
  } catch (error) {
    console.error('Error in batch update users:', error);
    throw error;
  }
};

/**
 * Search users by display name or email
 */
export const searchUsers = async (
  searchTerm: string,
  limitCount: number = 20
): Promise<UserEntity[]> => {
  try {
    const usersRef = collection(firestore, 'users');

    // Note: Firestore doesn't support full-text search natively
    // This is a basic implementation that searches by exact match or prefix
    const queries = [
      query(
        usersRef,
        where('displayName', '>=', searchTerm),
        where('displayName', '<=', searchTerm + '\uf8ff'),
        limit(limitCount)
      ),
      query(
        usersRef,
        where('email', '>=', searchTerm),
        where('email', '<=', searchTerm + '\uf8ff'),
        limit(limitCount)
      )
    ];

    const results: UserEntity[] = [];
    const seenIds = new Set<string>();

    for (const q of queries) {
      const querySnapshot = await getDocs(q);
      querySnapshot.docs.forEach(doc => {
        if (!seenIds.has(doc.id)) {
          seenIds.add(doc.id);
          results.push({
            id: doc.id,
            ...doc.data()
          } as UserEntity);
        }
      });
    }

    return results.slice(0, limitCount);
  } catch (error) {
    console.error(`Error searching users with term "${searchTerm}":`, error);
    throw error;
  }
};

/**
 * Get user statistics
 */
export const getUserStats = async (): Promise<{
  totalUsers: number;
  adminUsers: number;
  regularUsers: number;
  usersWithWallets: number;
  usersWithTelegram: number;
}> => {
  try {
    const usersRef = collection(firestore, 'users');
    const allUsersSnapshot = await getDocs(usersRef);

    let totalUsers = 0;
    let adminUsers = 0;
    let regularUsers = 0;
    let usersWithWallets = 0;
    let usersWithTelegram = 0;

    allUsersSnapshot.docs.forEach(doc => {
      const userData = doc.data() as UserEntity;
      totalUsers++;

      if (userData.role === 'admin') {
        adminUsers++;
      } else {
        regularUsers++;
      }

      if (userData.ton_wallet_address) {
        usersWithWallets++;
      }

      if (userData.tg_id) {
        usersWithTelegram++;
      }
    });

    return {
      totalUsers,
      adminUsers,
      regularUsers,
      usersWithWallets,
      usersWithTelegram
    };
  } catch (error) {
    console.error('Error getting user statistics:', error);
    throw error;
  }
};
