import { doc, getDoc, setDoc } from "firebase/firestore";
import { firestore } from "@/root-context";

export interface AppConfig {
  depositFee: number; // Static TON value
  withdrawFee: number; // Static TON value
  referrer_fee: number; // BPS
  reject_order_fee: number; // BPS
  purchase_fee: number; // BPS
  min_withdrawal_amount: number; // Static TON value
  max_withdrawal_amount: number; // Static TON value
}

const APP_CONFIG_COLLECTION = "app_config";
const APP_CONFIG_DOC_ID = "fees";

export const loadFeesConfig = async (): Promise<AppConfig | null> => {
  try {
    const configDoc = await getDoc(
      doc(firestore, APP_CONFIG_COLLECTION, APP_CONFIG_DOC_ID)
    );

    if (configDoc.exists()) {
      return configDoc.data() as AppConfig;
    }

    return null;
  } catch (error) {
    console.error("Error loading fees config:", error);
    throw error;
  }
};

export const updateFeesConfig = async (config: AppConfig): Promise<void> => {
  try {
    await setDoc(
      doc(firestore, APP_CONFIG_COLLECTION, APP_CONFIG_DOC_ID),
      config
    );
  } catch (error) {
    console.error("Error updating fees config:", error);
    throw error;
  }
};
