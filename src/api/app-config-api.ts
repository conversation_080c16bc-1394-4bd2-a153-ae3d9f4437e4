import { doc, getDoc } from 'firebase/firestore';
import { firestore } from '@/root-context';

export interface AppConfig {
  id: string;
  minDepositAmount: number; // in TON
  depositFee: number; // in TON
  withdrawalFee: number; // in TON
  maxDepositAmount?: number; // in TON
  maintenanceMode?: boolean;
  updatedAt?: Date;
}

/**
 * Get app configuration from Firebase
 */
export const getAppConfig = async (): Promise<AppConfig | null> => {
  try {
    const configRef = doc(firestore, 'app_config', 'main');
    const configDoc = await getDoc(configRef);
    
    if (!configDoc.exists()) {
      console.warn('App config document not found, using defaults');
      // Return default config if not found
      return {
        id: 'main',
        minDepositAmount: 1, // 1 TON
        depositFee: 0.1, // 0.1 TON
        withdrawalFee: 0.1, // 0.1 TON
        maxDepositAmount: 1000, // 1000 TON
        maintenanceMode: false
      };
    }
    
    return {
      id: configDoc.id,
      ...configDoc.data()
    } as AppConfig;
  } catch (error) {
    console.error('Error getting app config:', error);
    throw error;
  }
};

/**
 * Get minimum deposit amount from config
 */
export const getMinDepositAmount = async (): Promise<number> => {
  try {
    const config = await getAppConfig();
    return config?.minDepositAmount || 1;
  } catch (error) {
    console.error('Error getting min deposit amount:', error);
    return 1; // Default fallback
  }
};

/**
 * Get deposit fee from config
 */
export const getDepositFee = async (): Promise<number> => {
  try {
    const config = await getAppConfig();
    return config?.depositFee || 0.1;
  } catch (error) {
    console.error('Error getting deposit fee:', error);
    return 0.1; // Default fallback
  }
};
