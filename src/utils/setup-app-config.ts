import { doc, setDoc } from 'firebase/firestore';
import { firestore } from '@/root-context';

/**
 * Setup initial app configuration in Firebase
 * Run this once to create the app_config document
 */
export const setupAppConfig = async () => {
  try {
    const configRef = doc(firestore, 'app_config', 'main');
    
    const defaultConfig = {
      minDepositAmount: 1, // 1 TON minimum deposit
      depositFee: 0.1, // 0.1 TON deposit fee
      withdrawalFee: 0.1, // 0.1 TON withdrawal fee
      maxDepositAmount: 1000, // 1000 TON maximum deposit
      maintenanceMode: false,
      updatedAt: new Date(),
      createdAt: new Date()
    };

    await setDoc(configRef, defaultConfig);
    console.log('App config created successfully:', defaultConfig);
    return defaultConfig;
  } catch (error) {
    console.error('Error setting up app config:', error);
    throw error;
  }
};

// Helper function to update specific config values
export const updateAppConfig = async (updates: Partial<{
  minDepositAmount: number;
  depositFee: number;
  withdrawalFee: number;
  maxDepositAmount: number;
  maintenanceMode: boolean;
}>) => {
  try {
    const configRef = doc(firestore, 'app_config', 'main');
    
    const updateData = {
      ...updates,
      updatedAt: new Date()
    };

    await setDoc(configRef, updateData, { merge: true });
    console.log('App config updated successfully:', updateData);
    return updateData;
  } catch (error) {
    console.error('Error updating app config:', error);
    throw error;
  }
};
