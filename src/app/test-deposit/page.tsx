"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { DepositDrawer } from '@/components/DepositDrawer';
import { setupAppConfig } from '@/utils/setup-app-config';
import { getAppConfig } from '@/api/app-config-api';
import { toast } from 'sonner';

export default function TestDeposit() {
  const [showDepositDrawer, setShowDepositDrawer] = useState(false);
  const [config, setConfig] = useState<any>(null); // eslint-disable-line @typescript-eslint/no-explicit-any

  const handleSetupConfig = async () => {
    try {
      const result = await setupAppConfig();
      toast.success('App config created successfully!');
      console.log('Config created:', result);
    } catch (error) {
      toast.error('Failed to create app config');
      console.error('Error:', error);
    }
  };

  const handleGetConfig = async () => {
    try {
      const result = await getAppConfig();
      setConfig(result);
      toast.success('App config loaded successfully!');
      console.log('Config loaded:', result);
    } catch (error) {
      toast.error('Failed to load app config');
      console.error('Error:', error);
    }
  };

  return (
    <div className="p-8 max-w-md mx-auto">
      <h1 className="text-2xl font-bold mb-6">Test Deposit Functionality</h1>
      
      <div className="space-y-4">
        <Button 
          onClick={handleSetupConfig}
          className="w-full"
          variant="outline"
        >
          Setup App Config in Firebase
        </Button>
        
        <Button 
          onClick={handleGetConfig}
          className="w-full"
          variant="outline"
        >
          Load App Config
        </Button>
        
        <Button 
          onClick={() => setShowDepositDrawer(true)}
          className="w-full bg-ton-main hover:bg-ton-main/90"
        >
          Test Deposit Drawer
        </Button>
      </div>

      {config && (
        <div className="mt-6 p-4 bg-gray-100 rounded-lg">
          <h3 className="font-semibold mb-2">Current Config:</h3>
          <pre className="text-sm">{JSON.stringify(config, null, 2)}</pre>
        </div>
      )}

      <DepositDrawer 
        open={showDepositDrawer} 
        onOpenChange={setShowDepositDrawer} 
      />
    </div>
  );
}
