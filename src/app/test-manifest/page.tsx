"use client";

import { useEffect, useState } from 'react';

export default function TestManifest() {
  const [manifestData, setManifestData] = useState<any>(null);
  const [error, setError] = useState<string>('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const testManifest = async () => {
      try {
        console.log('Testing manifest URL...');
        
        // Test relative URL
        const response = await fetch('/tonconnect-manifest.json');
        console.log('Response status:', response.status);
        console.log('Response headers:', Object.fromEntries(response.headers.entries()));
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        console.log('Manifest data:', data);
        setManifestData(data);
      } catch (err) {
        console.error('Manifest test failed:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    testManifest();
  }, []);

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">Manifest Test</h1>
      
      {loading && <p>Loading manifest...</p>}
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <strong>Error:</strong> {error}
        </div>
      )}
      
      {manifestData && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          <strong>Success!</strong> Manifest loaded successfully.
          <pre className="mt-2 text-sm">{JSON.stringify(manifestData, null, 2)}</pre>
        </div>
      )}
      
      <div className="mt-4">
        <h2 className="text-lg font-semibold mb-2">Test URLs:</h2>
        <ul className="list-disc pl-5">
          <li>
            <a 
              href="/tonconnect-manifest.json" 
              target="_blank" 
              className="text-blue-600 hover:underline"
            >
              /tonconnect-manifest.json (relative)
            </a>
          </li>
          <li>
            <a 
              href="https://192.168.3.133:3000/tonconnect-manifest.json" 
              target="_blank" 
              className="text-blue-600 hover:underline"
            >
              https://192.168.3.133:3000/tonconnect-manifest.json (absolute)
            </a>
          </li>
        </ul>
      </div>
    </div>
  );
}
