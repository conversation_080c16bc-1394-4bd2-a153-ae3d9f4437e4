"use client";

import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { Button } from "@/components/ui/button";
import { Minus, Plus, User, Lock, ChevronDown, LogOut } from "lucide-react";
import Image from "next/image";
import { useTonConnectUI } from "@tonconnect/ui-react";
import { Address } from "@ton/core";
import { useTelegramAuth } from '@/hooks/useTelegramAuth';
import { useRootContext } from '@/root-context';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import { useRouter } from 'next/navigation';
import { updateUserWallet } from '@/api/user-api';
import { DepositDrawer } from '@/components/DepositDrawer';
import { WithdrawDrawer } from '@/components/WithdrawDrawer';

// Constants
const BUTTON_CLASSES = {
  actionButton:
    "w-6 h-6 rounded-full bg-ton-main hover:bg-ton-main/80 transition-all ease-in-out",
  walletButton:
    "bg-ton-main hover:bg-ton-main hover:scale-110 transition-all ease-in-out text-white rounded-full text-xs font-semibold py-1 px-2 flex-shrink-0 min-w-0",
};

const TON_LOGO_PROPS = {
  src: "/ton.svg",
  alt: "TON Logo",
};

// Reusable TON Logo component
const TonLogo = ({
  size = 32,
  className = "",
}: {
  size?: number;
  className?: string;
}) => (
  <Image {...TON_LOGO_PROPS} width={size} height={size} className={className} />
);

export default function MarketplaceHeader() {
  const router = useRouter();
  const [tonConnectUI] = useTonConnectUI();
  const [tonWalletAddress, setTonWalletAddress] = useState<string>("");
  const [isConnecting, setIsConnecting] = useState(false);
  const [showWalletDropdown, setShowWalletDropdown] = useState(false);
  const [isAuthenticating, setIsAuthenticating] = useState(false);
  const [showDepositDrawer, setShowDepositDrawer] = useState(false);
  const [showWithdrawDrawer, setShowWithdrawDrawer] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const { currentUser, setCurrentUser } = useRootContext();

  const { authenticate, isLoading } = useTelegramAuth({
    onSuccess: async () => {
      toast.success("Authenticated successfully");

      // If we were trying to connect wallet, start the connection process
      if (isAuthenticating) {
        setIsAuthenticating(false);
        setIsConnecting(true);
        try {
          await tonConnectUI.openModal();
        } catch (error) {
          console.error('Error opening wallet modal after auth:', error);
          setIsConnecting(false);
        }
      }
    },
    onError: (error) => {
      toast.error(`Authentication failed: ${error}`);
      setIsAuthenticating(false);
    },
  });

  // Initialize wallet address from connected wallet, not from user data
  useEffect(() => {
    if (tonConnectUI.account?.address) {
      setTonWalletAddress(tonConnectUI.account.address);
    } else {
      setTonWalletAddress('');
    }
  }, [tonConnectUI.account?.address]);

  // Computed values using useMemo for performance
  const balanceInfo = useMemo(() => {
    if (!currentUser?.balance) {
      return { available: '0.00', locked: 0, hasLocked: false };
    }

    const available = (
      currentUser.balance.sum - currentUser.balance.locked
    ).toFixed(2);
    const locked = currentUser.balance.locked;
    const hasLocked = locked > 0;

    return { available, locked, hasLocked };
  }, [currentUser?.balance]);

  const onProfileButtonClick = () => {
    if (currentUser) {
      router.push("/profile");
    } else {
      authenticate();
    }
  };

  const handleDepositClick = () => {
    setShowDepositDrawer(true);
  };

  const handleWithdrawClick = () => {
    setShowWithdrawDrawer(true);
  };

  const handleWalletConnection = useCallback(async (address: string) => {
    try {
      setTonWalletAddress(address);

      // Update user's wallet address in Firebase if user is authenticated
      if (currentUser?.id) {
        try {
          // Parse the address to get both formats
          const parsedAddress = Address.parse(address);
          const userFriendlyAddress = parsedAddress.toString();
          const updatedUser = await updateUserWallet(
            currentUser.id,
            userFriendlyAddress,
          );
          setCurrentUser(updatedUser);
          toast.success("Wallet connected and saved to profile");
        } catch (error) {
          console.error('Error saving wallet to profile:', error);
          console.error('Error details:', {
            userId: currentUser.id,
            address,
            error: error instanceof Error ? error.message : error
          });
          toast.error(`Wallet connected but failed to save to profile: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }
    } catch (error) {
      console.error('Error connecting wallet:', error);
      toast.error("Failed to connect wallet");
    } finally {
      setIsConnecting(false);
    }
  }, [currentUser?.id, setCurrentUser]);

  const handleWalletDisconnection = useCallback(async () => {
    try {
      setTonWalletAddress('');
      setShowWalletDropdown(false);
      toast.success("Wallet disconnected");
    } catch (error) {
      console.error('Error disconnecting wallet:', error);
    }
  }, []);

  useEffect(() => {
    const unsubscribe = tonConnectUI.onStatusChange(async (wallet) => {
      if (wallet && isConnecting) {
        await handleWalletConnection(wallet.account.address);
      } else if (!wallet && !isConnecting) {
        await handleWalletDisconnection();
      }
    });

    return () => {
      unsubscribe();
    };
  }, [
    tonConnectUI,
    handleWalletConnection,
    handleWalletDisconnection,
    isConnecting,
  ]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowWalletDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Handle modal close to reset connecting state
  useEffect(() => {
    const handleModalStateChange = () => {
      // Check if modal is closed and reset connecting state
      if (!tonConnectUI.modal.state?.status || tonConnectUI.modal.state.status === 'closed') {
        setIsConnecting(false);
      }
    };

    const unsubscribe = tonConnectUI.modal.onStateChange(handleModalStateChange);
    return () => {
      unsubscribe();
    };
  }, [tonConnectUI]);

  const handleWalletAction = async () => {
    console.log('TonConnect status:', tonConnectUI.account);
    console.log('Trying to connect wallet...');

    // If user is not authenticated, authenticate first
    if (!currentUser) {
      setIsAuthenticating(true);
      try {
        await authenticate();
        // After successful authentication, the wallet connection will be handled
        // by the authentication success callback
      } catch (error) {
        console.error('Authentication failed:', error);
        setIsAuthenticating(false);
        return;
      }
      setIsAuthenticating(false);
    }

    // If wallet is already connected, show dropdown
    if (tonConnectUI.account?.address) {
      setShowWalletDropdown(!showWalletDropdown);
    } else {
      // Start wallet connection process
      setIsConnecting(true);
      try {
        console.log('Opening TonConnect modal...');
        await tonConnectUI.openModal();
      } catch (error) {
        console.error('Error opening wallet modal:', error);
        setIsConnecting(false);
      }
    }
  };

  const handleDisconnectWallet = async () => {
    try {
      await tonConnectUI.disconnect();
      setTonWalletAddress('');
      setShowWalletDropdown(false);
    } catch (error) {
      console.error('Error disconnecting wallet:', error);
    }
  };

  const formatAddress = (address: string) => {
    if (!address) return "";
    try {
      const addr = Address.parse(address);
      const formatted = addr.toString();
      return `${formatted.slice(0, 6)}...${formatted.slice(-6)}`;
    } catch {
      return `${address.slice(0, 6)}...${address.slice(-6)}`;
    }
  };

  // Action button component for DRY
  const ActionButton = ({
    icon: Icon,
    onClick,
  }: {
    icon: typeof Plus;
    onClick?: () => void;
  }) => (
    <Button
      size="icon"
      variant="default"
      className={BUTTON_CLASSES.actionButton}
      onClick={onClick}
    >
      <Icon className="w-4 h-4 stroke-[2.5]" />
    </Button>
  );

  return (
    <header className="bg-ton-black text-white p-2">
      <div className="flex items-center justify-between gap-1 min-w-0">
        <div className="flex items-center gap-1 bg-ton-black/60 min-w-0 flex-1">
          {/* Balance Section */}
          <div className="flex items-center gap-1 min-w-0 flex-shrink">
            <TonLogo size={20} className="w-5 h-5 flex-shrink-0" />
            <div className="flex items-center gap-1 min-w-0">
              <span className="text-sm font-bold truncate">
                {balanceInfo.available}
              </span>
              {balanceInfo.hasLocked && (
                <span className="text-[10px] text-gray-300 flex items-center gap-1 flex-shrink-0">
                  (<Lock className="w-2 h-2" />
                  {balanceInfo.locked.toFixed(2)})
                </span>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-1">
            <ActionButton icon={Plus} onClick={handleDepositClick} />
            <ActionButton icon={Minus} onClick={handleWithdrawClick} />
          </div>

          {/* Profile Button */}
          <div
            onClick={onProfileButtonClick}
            className={cn(
              "w-6 h-6 cursor-pointer rounded-full overflow-hidden bg-gray-300 flex items-center justify-center",
              isLoading && "pointer-events-none"
            )}
          >
            {currentUser?.photoURL ? (
              <img
                src={currentUser.photoURL}
                alt="User avatar"
                className="w-full h-full object-cover"
              />
            ) : (
              <User className="w-3 h-3 text-gray-600" />
            )}
          </div>
        </div>

        {/* Wallet Button */}
        <div className="relative" ref={dropdownRef}>
          <Button
            variant="default"
            size="sm"
            className={cn(BUTTON_CLASSES.walletButton, 'gap-1 max-w-[110px] xs:max-w-fit')}
            onClick={handleWalletAction}
            disabled={isConnecting || isAuthenticating}
          >
            <TonLogo size={12} className="w-4 h-4" />
            <span className="ml-0.5 text-xs truncate">
              {isAuthenticating
                ? 'Authenticating...'
                : isConnecting
                  ? 'Connecting...'
                  : tonWalletAddress
                    ? formatAddress(tonWalletAddress)
                    : 'Connect'
              }
            </span>
            {tonWalletAddress && (
              <ChevronDown className="w-3 h-3 ml-1" />
            )}
          </Button>

          {/* Dropdown Menu */}
          {showWalletDropdown && tonWalletAddress && (
            <div className="absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg py-1 min-w-[120px] z-50">
              <button
                onClick={handleDisconnectWallet}
                className="w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
              >
                <LogOut className="w-3 h-3" />
                Disconnect
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Deposit Drawer */}
      <DepositDrawer
        open={showDepositDrawer}
        onOpenChange={setShowDepositDrawer}
      />

      {/* Withdraw Drawer */}
      <WithdrawDrawer
        open={showWithdrawDrawer}
        onOpenChange={setShowWithdrawDrawer}
      />
    </header>
  );
}
