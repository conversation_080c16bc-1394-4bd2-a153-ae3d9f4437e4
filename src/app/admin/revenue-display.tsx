"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  getMarketplaceRevenue,
  MarketplaceRevenue,
  withdrawRevenue,
} from "@/api/revenue-api";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { Loader2, TrendingUp, Send, AlertTriangle } from "lucide-react";

const revenueDistributionSchema = z.object({
  withdrawAmount: z.number().min(0.1, "Minimum withdrawal is 0.1 TON"),
  alexWallet: z.string().min(1, "Alex wallet address is required"),
  mikeWallet: z.string().min(1, "Mike wallet address is required"),
  pittWallet: z.string().min(1, "Pitt wallet address is required"),
});

type RevenueDistributionFormData = z.infer<typeof revenueDistributionSchema>;

export const RevenueDisplay = () => {
  const [revenue, setRevenue] = useState<MarketplaceRevenue | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isWithdrawing, setIsWithdrawing] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const { toast } = useToast();

  const form = useForm<RevenueDistributionFormData>({
    resolver: zodResolver(revenueDistributionSchema),
    defaultValues: {
      withdrawAmount: 0,
      alexWallet: "",
      mikeWallet: "",
      pittWallet: "",
    },
  });

  const loadRevenue = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const revenueData = await getMarketplaceRevenue();
      setRevenue(revenueData);
    } catch (err) {
      console.error("Error loading revenue:", err);
      setError("Failed to load revenue data");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadRevenue();
  }, []);

  const onWithdrawRevenue = async (data: RevenueDistributionFormData) => {
    try {
      setIsWithdrawing(true);

      // Validate that there's enough revenue (must keep 10 TON)
      const availableRevenue = revenue ? revenue.sum - revenue.locked : 0;
      if (availableRevenue < 10) {
        toast({
          title: "Error",
          description:
            "Cannot withdraw when revenue balance is less than 10 TON.",
          variant: "destructive",
        });
        return;
      }

      if (data.withdrawAmount > availableRevenue - 10) {
        toast({
          title: "Error",
          description: `Cannot withdraw more than ${(
            availableRevenue - 10
          ).toFixed(4)} TON. Must keep 10 TON minimum.`,
          variant: "destructive",
        });
        return;
      }

      // Call withdrawRevenue cloud function
      const result = await withdrawRevenue(data);

      toast({
        title: "Success",
        description: result.message,
      });

      // Reload revenue data
      await loadRevenue();
      form.reset();
      setShowConfirmDialog(false);
    } catch (error) {
      console.error("Error withdrawing revenue:", error);
      toast({
        title: "Error",
        description: "Failed to withdraw revenue. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsWithdrawing(false);
    }
  };

  const onFormSubmit = () => {
    // Show confirmation dialog instead of immediately submitting
    setShowConfirmDialog(true);
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <TrendingUp className="h-4 w-4" />
            Marketplace Revenue
          </CardTitle>
          <CardDescription className="text-sm">
            Total revenue from fees
          </CardDescription>
        </CardHeader>
        <CardContent className="flex items-center justify-center py-4 pt-0">
          <Loader2 className="h-4 w-4 animate-spin" />
          <span className="ml-2 text-sm">Loading...</span>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <TrendingUp className="h-4 w-4" />
            Marketplace Revenue
          </CardTitle>
          <CardDescription className="text-sm">
            Total revenue from fees
          </CardDescription>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="text-red-600 text-sm">{error}</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <TrendingUp className="h-4 w-4" />
          Marketplace Revenue
        </CardTitle>
        <CardDescription className="text-sm">
          Total revenue from fees
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Total:</span>
            <span className="text-xl font-bold">
              {revenue?.sum.toFixed(4)} TON
            </span>
          </div>
          {revenue && revenue.locked > 0 && (
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Locked:</span>
              <span>{revenue.locked.toFixed(4)} TON</span>
            </div>
          )}
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Available:</span>
            <span>
              {revenue ? (revenue.sum - revenue.locked).toFixed(4) : "0.0000"}{" "}
              TON
            </span>
          </div>
        </div>
        <div className="mt-6 pt-4 border-t">
          <h4 className="font-medium mb-4 text-sm flex items-center gap-2">
            <Send className="h-4 w-4" />
            Distribute Revenue
          </h4>

          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(onFormSubmit)}
              className="space-y-4"
            >
              <FormField
                control={form.control}
                name="withdrawAmount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Withdrawal Amount (TON)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="0.00"
                        {...field}
                        onChange={(e) => field.onChange(Number(e.target.value))}
                      />
                    </FormControl>
                    <FormDescription>
                      Available for withdrawal:{" "}
                      {revenue
                        ? Math.max(
                            0,
                            revenue.sum - revenue.locked - 10
                          ).toFixed(4)
                        : "0.0000"}{" "}
                      TON
                      {field.value > 0 && (
                        <span className="block mt-1">
                          Each person will receive:{" "}
                          {(field.value / 3).toFixed(4)} TON
                        </span>
                      )}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 gap-4">
                <FormField
                  control={form.control}
                  name="alexWallet"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Alex Wallet Address</FormLabel>
                      <FormControl>
                        <Input placeholder="UQ..." {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="mikeWallet"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Mike Wallet Address</FormLabel>
                      <FormControl>
                        <Input placeholder="UQ..." {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="pittWallet"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Pitt Wallet Address</FormLabel>
                      <FormControl>
                        <Input placeholder="UQ..." {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <Button
                type="submit"
                disabled={
                  isWithdrawing || !revenue || revenue.sum - revenue.locked < 10
                }
                className="w-full"
              >
                {isWithdrawing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Distributing Revenue...
                  </>
                ) : (
                  <>
                    <Send className="mr-2 h-4 w-4" />
                    Distribute Revenue
                  </>
                )}
              </Button>
            </form>
          </Form>

          {/* Confirmation Dialog */}
          <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-orange-500" />
                  Confirm Revenue Distribution
                </DialogTitle>
                <DialogDescription>
                  Are you sure you want to distribute{" "}
                  <strong>
                    {form.watch("withdrawAmount")?.toFixed(4)} TON
                  </strong>
                  ?
                  <br />
                  <br />
                  Each person will receive:{" "}
                  <strong>
                    {(form.watch("withdrawAmount") / 3)?.toFixed(4)} TON
                  </strong>
                  <br />
                  <br />
                  This action cannot be undone.
                </DialogDescription>
              </DialogHeader>
              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setShowConfirmDialog(false)}
                  disabled={isWithdrawing}
                >
                  Cancel
                </Button>
                <Button
                  onClick={() => onWithdrawRevenue(form.getValues())}
                  disabled={isWithdrawing}
                  variant="destructive"
                >
                  {isWithdrawing ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Distributing...
                    </>
                  ) : (
                    "Confirm Distribution"
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </CardContent>
    </Card>
  );
};
