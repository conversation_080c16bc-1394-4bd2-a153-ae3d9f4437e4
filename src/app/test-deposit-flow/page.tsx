"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { DepositDrawer } from '@/components/DepositDrawer';
import { setupAppConfig, updateAppConfig } from '@/utils/setup-app-config';
import { getAppConfig } from '@/api/app-config-api';
import { toast } from 'sonner';
import { Plus } from 'lucide-react';

export default function TestDepositFlow() {
  const [showDepositDrawer, setShowDepositDrawer] = useState(false);
  const [config, setConfig] = useState<any>(null); // eslint-disable-line @typescript-eslint/no-explicit-any

  const handleSetupConfig = async () => {
    try {
      const result = await setupAppConfig();
      toast.success('App config created successfully!');
      console.log('Config created:', result);
      setConfig(result);
    } catch (error) {
      toast.error('Failed to create app config');
      console.error('Error:', error);
    }
  };

  const handleGetConfig = async () => {
    try {
      const result = await getAppConfig();
      setConfig(result);
      toast.success('App config loaded successfully!');
      console.log('Config loaded:', result);
    } catch (error) {
      toast.error('Failed to load app config');
      console.error('Error:', error);
    }
  };

  const handleUpdateConfig = async () => {
    try {
      const result = await updateAppConfig({
        minDepositAmount: 0.5, // Lower for testing
        depositFee: 0.05, // Lower for testing
      });
      toast.success('App config updated for testing!');
      console.log('Config updated:', result);
      await handleGetConfig(); // Refresh
    } catch (error) {
      toast.error('Failed to update app config');
      console.error('Error:', error);
    }
  };

  return (
    <div className="p-8 max-w-md mx-auto">
      <h1 className="text-2xl font-bold mb-6">Test Complete Deposit Flow</h1>
      
      <div className="space-y-4">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-sm">
          <h3 className="font-semibold text-blue-800 mb-2">Test Flow:</h3>
          <ol className="list-decimal pl-5 space-y-1 text-blue-700">
            <li>Setup or load app config</li>
            <li>Click the Plus button to open deposit drawer</li>
            <li>Enter deposit amount and click Deposit</li>
            <li>After transaction, countdown popup appears</li>
            <li>Wait 60 seconds or close manually</li>
            <li>User balance gets refetched automatically</li>
          </ol>
        </div>

        <Button 
          onClick={handleSetupConfig}
          className="w-full"
          variant="outline"
        >
          1. Setup App Config in Firebase
        </Button>
        
        <Button 
          onClick={handleGetConfig}
          className="w-full"
          variant="outline"
        >
          2. Load Current App Config
        </Button>

        <Button 
          onClick={handleUpdateConfig}
          className="w-full"
          variant="outline"
        >
          3. Update Config for Testing (Lower amounts)
        </Button>
        
        <div className="flex items-center justify-center">
          <Button 
            onClick={() => setShowDepositDrawer(true)}
            className="flex items-center gap-2 bg-ton-main hover:bg-ton-main/90"
          >
            <Plus className="w-4 h-4" />
            4. Test Deposit Flow
          </Button>
        </div>
      </div>

      {config && (
        <div className="mt-6 p-4 bg-gray-100 rounded-lg">
          <h3 className="font-semibold mb-2">Current Config:</h3>
          <div className="text-sm space-y-1">
            <div>Min Deposit: <strong>{config.minDepositAmount} TON</strong></div>
            <div>Deposit Fee: <strong>{config.depositFee} TON</strong></div>
            <div>Withdrawal Fee: <strong>{config.withdrawalFee} TON</strong></div>
            <div>Max Deposit: <strong>{config.maxDepositAmount} TON</strong></div>
            <div>Maintenance: <strong>{config.maintenanceMode ? 'Yes' : 'No'}</strong></div>
          </div>
        </div>
      )}

      <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg text-sm">
        <h3 className="font-semibold text-yellow-800 mb-2">Environment Info:</h3>
        <div className="text-yellow-700 space-y-1">
          <div>Marketplace Wallet: <code className="text-xs">{process.env.NEXT_PUBLIC_MARKETPLACE_WALLET_ADDRESS}</code></div>
          <div>Server: <code>http://*************:3002</code></div>
        </div>
      </div>

      <DepositDrawer 
        open={showDepositDrawer} 
        onOpenChange={setShowDepositDrawer} 
      />
    </div>
  );
}
