// Development configuration for local testing
export const DEV_CONFIG = {
  // Local development server
  LOCAL_HOST: '*************',
  LOCAL_PORT: 3002,
  
  // Base URLs
  get BASE_URL() {
    return `http://${this.LOCAL_HOST}:${this.LOCAL_PORT}`;
  },
  
  get MANIFEST_URL() {
    return `${this.BASE_URL}/tonconnect-manifest.json`;
  },
  
  // For testing
  MANIFEST_CONTENT: {
    url: `http://*************:3002`,
    name: "Marketplace UI",
    iconUrl: `http://*************:3002/ton.svg`
  }
};

// Helper to check if we're in development
export const isDevelopment = process.env.NODE_ENV === 'development';

// Get the appropriate manifest URL based on environment
export const getManifestUrl = () => {
  if (isDevelopment) {
    return DEV_CONFIG.MANIFEST_URL;
  }
  // Production URL would go here
  return '/tonconnect-manifest.json';
};
