import { Context } from "telegraf";
import { completePurchaseByBot } from "../firebase-service";
import {
  createEchoModeKeyboard,
  createOrderSuccessKeyboard,
  createOrderErrorKeyboard,
} from "../utils/keyboards";
import { getUserSession, clearUserSession } from "../services/session";
import { logMessageDetails } from "../utils/messageLogger";

export const handleMessage = async (ctx: Context) => {
  try {
    const userId = ctx.from?.id?.toString();
    if (!userId) return;

    const session = getUserSession(userId);
    const pendingOrderId = session?.pendingOrderId;
    const echoMode = session?.echoMode;

    // Handle echo mode first
    if (echoMode) {
      await handleEchoMode(ctx, userId);
      return;
    }

    if (!pendingOrderId) {
      // No pending order and not in echo mode, ignore the message
      return;
    }

    // Handle order completion
    await handleOrderCompletion(ctx, userId, pendingOrderId);
  } catch (error) {
    console.error("Error handling message:", error);
    ctx.reply("❌ An error occurred while processing your message.");
  }
};

const handleEchoMode = async (ctx: Context, userId: string) => {
  if (!ctx.message) return;

  logMessageDetails(userId, ctx.message);

  // Echo the message back to the user
  try {
    if (ctx.chat) {
      await ctx.copyMessage(ctx.chat.id);
    }
    ctx.reply(
      "✅ Gift echoed successfully!\n\n" +
        "📝 Check the console for logged data.\n\n" +
        "Send another gift or click Cancel to exit echo mode.",
      createEchoModeKeyboard()
    );
  } catch (error) {
    console.error("Error echoing message:", error);
    ctx.reply(
      "❌ Failed to echo the gift. This might be due to message type restrictions.\n\n" +
        "📝 However, the data was still logged to the console.",
      createEchoModeKeyboard()
    );
  }
};

const handleOrderCompletion = async (
  ctx: Context,
  userId: string,
  pendingOrderId: string
) => {
  // Clear the pending order
  clearUserSession(userId);

  ctx.reply("🔄 Processing your gift and completing the order...");

  try {
    const result = await completePurchaseByBot(pendingOrderId);

    if (result.success) {
      ctx.reply(
        "✅ Order Completed Successfully!\n\n" +
          `📦 Order #${result.order.number}\n` +
          `💰 Seller received: ${result.netAmountToSeller} TON\n` +
          `💸 Fee applied: ${result.feeAmount} TON\n\n` +
          "🎉 Thank you for using our marketplace!",
        createOrderSuccessKeyboard()
      );
    } else {
      throw new Error("Purchase completion failed");
    }
  } catch (completionError) {
    console.error("Error completing purchase:", completionError);

    // Return the gift to the user
    ctx.reply(
      "❌ Failed to complete the order. Your gift is being returned to you.\n\n" +
        "Error: " +
        (completionError as Error).message +
        "\n\n" +
        "Please try again later or contact support.",
      createOrderErrorKeyboard()
    );

    // Forward the original message back to the user
    if (ctx.message && ctx.from) {
      try {
        await ctx.forwardMessage(ctx.from.id);
      } catch (forwardError) {
        console.error("Error forwarding message back:", forwardError);
        ctx.reply(
          "⚠️ Unable to return your gift automatically. Please save it manually."
        );
      }
    }
  }
};
