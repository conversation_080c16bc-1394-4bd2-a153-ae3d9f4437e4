import TonWeb from "tonweb";
import { mnemonicToKeyPair } from "tonweb-mnemonic";

const receiver = "UQAW8pzkwoPgsiKi6f-RVvE-VXOoHJ_dhEZ5-HkES7NdIlSQ";

const MARKETPLACE_WALLET_MNEMONIC =
  "spice myth collect display ship legend seminar injury setup voice faith steel erase captain supply cave range author neither decrease hire update mechanic state";

export const testWithdraw = async () => {
  try {
    const tonRpcUrl =
      "https://misty-crimson-friday.ton-mainnet.quiknode.pro/bd21cc29f2080adf31da2fe2af76f1f6df3d7b5c";

    const tonweb = new TonWeb(new TonWeb.HttpProvider(tonRpcUrl));

    const marketplaceMnemonic = MARKETPLACE_WALLET_MNEMONIC;
    const keyPair = await mnemonicToKeyPair(marketplaceMnemonic.split(" "));

    const WalletClass = tonweb.wallet.all.v4R2;
    const marketplaceWallet = new WalletClass(tonweb.provider, {
      publicKey: keyPair.publicKey,
      wc: 0, // workchain
    });

    const seqno = 0;

    const amountInNanotons = TonWeb.utils.toNano("1"); // 1 TON

    await marketplaceWallet.methods
      .transfer({
        secretKey: keyPair.secretKey,
        toAddress: receiver,
        amount: amountInNanotons,
        seqno: seqno,
        payload: "Withdrawal from marketplace",
      })
      .send();

    console.log("Withdrawal transaction sent successfully");
  } catch (error) {
    debugger;
    console.error("Error in testWithdraw function:", error);
  }
};
