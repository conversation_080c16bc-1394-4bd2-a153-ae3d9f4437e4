import dotenv from "dotenv";
import { Telegraf } from "telegraf";
import {
  handleCompleteOrderButton,
  handleEchoGiftButton,
  handleGetMyOrdersButton,
  handleGetReferralLinkButton,
} from "./handlers/buttons";
import {
  handleBackToMenuCallback,
  handleBackToOrdersCallback,
  handleCancelEchoModeCallback,
  handleContactSupportCallback,
  handleOpenMarketplaceCallback,
  handleOrderCompletionCallback,
  handleOrderHelpCallback,
  handleOrderSelectionCallback,
} from "./handlers/callbacks";
import { handleHelpCommand, handleStartCommand } from "./handlers/commands";
import { handleMessage } from "./handlers/messages";

dotenv.config();

const BOT_TOKEN = process.env.BOT_TOKEN;

if (!BOT_TOKEN) {
  throw new Error("BOT_TOKEN is required in environment variables");
}

const bot = new Telegraf(BOT_TOKEN);

bot.use(async (ctx, next) => {
  try {
    if (ctx.update && "business_connection" in ctx.update) {
      const businessConnection = (ctx.update as any).business_connection;
      const businessAccountId = businessConnection.user_chat_id;

      console.log(`Business Account Connected: ID = ${businessAccountId}`);

      await ctx.telegram.sendMessage(
        businessAccountId,
        "Thanks for connecting me to your Business account!"
      );
      return;
    }

    if (ctx.update && "business_message" in ctx.update) {
      const businessMessage = (ctx.update as any).business_message;
      const businessConnectionId = businessMessage.business_connection_id;
      const senderId = businessMessage.from?.id;

      // First, get owned gifts to find the received gift
      const ownedGiftsResponse = await fetch(
        `https://api.telegram.org/bot${BOT_TOKEN}/getBusinessAccountGifts`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            business_connection_id: businessConnectionId,
            limit: 50, // Get more gifts to find the received one
          }),
        }
      );

      const ownedGiftsResult = (await ownedGiftsResponse.json()) as {
        ok: boolean;
        result?: {
          gifts: Array<{
            owned_gift_id: string;
            gift: {
              id: string;
              sticker: any;
              star_count: number;
              total_count?: number;
              remaining_count?: number;
              upgrade_star_count?: number;
            };
            date: number;
            is_saved: boolean;
            is_name_hidden: boolean;
            is_unique?: boolean; // Unique gifts can be transferred
          }>;
        };
        description?: string;
      };

      console.log("ownedGiftsResult", ownedGiftsResult.result?.gifts);
      console.log("ownedGiftsResult", ownedGiftsResult);

      if (
        ownedGiftsResult.ok &&
        ownedGiftsResult.result?.gifts &&
        ownedGiftsResult.result.gifts.length > 0
      ) {
        console.log(
          `Found ${ownedGiftsResult.result.gifts.length} total gifts in business account`
        );

        // Find the most recent gift (likely the one just received) and convert it to stars
        const recentGift = ownedGiftsResult.result.gifts[0];

        if (recentGift?.owned_gift_id) {
          console.log(
            `Converting gift ${recentGift.owned_gift_id} to stars and sending new gift to user ${senderId}`
          );

          console.log(
            `Successfully converted gift ${recentGift.owned_gift_id} to stars`
          );

          // Now send a new gift to the sender using the converted stars
          const sendGiftResponse = await fetch(
            `https://api.telegram.org/bot${BOT_TOKEN}/sendGift`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                business_connection_id: businessConnectionId,
                user_id: senderId,
                gift_id: recentGift.owned_gift_id, // Send the same type of gift back
              }),
            }
          );

          const sendGiftResult = (await sendGiftResponse.json()) as {
            ok: boolean;
            description?: string;
            error_code?: number;
          };

          if (sendGiftResult.ok) {
            console.log(`Successfully sent gift back to user ${senderId}`);
          } else {
            console.error("Failed to send gift back:", {
              error_code: sendGiftResult.error_code,
              description: sendGiftResult.description,
              senderId,
              giftId: recentGift.gift.id,
              businessConnectionId,
            });
          }

          console.log("No valid gift found to convert and echo back");
        }
      } else {
        console.log("No owned gifts found in business account");
        console.error("Failed to get owned gifts:", ownedGiftsResult);
      }
    }

    // Continue to next middleware for regular updates
    await next();
  } catch (error) {
    console.error("Error handling update:", error);
    await next();
  }
});

bot.start(handleStartCommand);
bot.help(handleHelpCommand);

bot.hears("🎁 Echo Gift", handleEchoGiftButton);
bot.hears("📋 Get My Orders", handleGetMyOrdersButton);
bot.hears("✅ Complete Order", handleCompleteOrderButton);
bot.hears("🔗 Get Referral Link", handleGetReferralLinkButton);

bot.action("order_help", handleOrderHelpCallback);
bot.action("contact_support", handleContactSupportCallback);
bot.action("open_marketplace", handleOpenMarketplaceCallback);
bot.action("cancel_echo_mode", handleCancelEchoModeCallback);
bot.action(/^order_(.+)$/, handleOrderSelectionCallback);
bot.action("back_to_orders", handleBackToOrdersCallback);
bot.action(/^complete_(.+)$/, handleOrderCompletionCallback);
bot.action("back_to_menu", handleBackToMenuCallback);

// Handle regular messages
bot.on("message", handleMessage);

bot.catch((err, ctx) => {
  console.error("Bot error:", err);
  ctx?.reply?.("Sorry, something went wrong. Please try again later.");
});

// Graceful shutdown handlers
process.once("SIGINT", () => {
  console.log("Received SIGINT, stopping bot...");
  bot.stop("SIGINT");
});

process.once("SIGTERM", () => {
  console.log("Received SIGTERM, stopping bot...");
  bot.stop("SIGTERM");
});

export default bot;
