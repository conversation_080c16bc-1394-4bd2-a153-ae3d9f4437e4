#!/bin/bash

# Firebase Cloud Functions Local Debugging Setup Script
# This script sets up everything needed for local debugging

set -e  # Exit on any error

echo "🚀 Setting up Firebase Cloud Functions local debugging environment..."
echo ""

# Check if we're in the right directory
if [ ! -f "firebase.json" ]; then
    echo "❌ Error: firebase.json not found. Please run this script from the marketplace-functions directory."
    exit 1
fi

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "❌ Error: Firebase CLI is not installed."
    echo "Install it with: npm install -g firebase-tools"
    exit 1
fi

echo "✅ Firebase CLI found: $(firebase --version)"

# Step 1: Install function dependencies
echo ""
echo "📦 Installing function dependencies..."
cd functions
npm install
echo "✅ Dependencies installed"

# Step 2: Create local environment file if it doesn't exist
echo ""
echo "🔧 Setting up local environment variables..."

if [ ! -f ".env.local" ]; then
    echo "Creating .env.local file..."
    cat > .env.local << 'EOF'
# Firebase Cloud Functions Local Environment Variables
# Edit these values for your local development

# Required for local development
TON_RPC_URL_MAINNET=https://misty-crimson-friday.ton-mainnet.quiknode.pro/bd21cc29f2080adf31da2fe2af76f1f6df3d7b5c
TON_MARKETPLACE_WALLET=UQCWcm7v_aEFlLEUmAYuryEfpepeRcFVSVIimfmCYmxbE7gI
TON_MARKETPLACE_WALLET_MNEMONIC="spice myth collect display ship legend seminar injury setup voice faith steel erase captain supply cave range author neither decrease hire update mechanic state"

# Telegram (optional for withdrawFunds testing)
TELEGRAM_BOT_TOKEN=**********************************************
TELEGRAM_API_ID=14431416
TELEGRAM_API_HASH=da8fa0a17dd9e0c1b9e420d73a39a710

# Firebase
FIREBASE_PROJECT_ID=your-project-id
NODE_ENV=development
EOF
    echo "✅ Created .env.local file"
    echo "⚠️  Please edit functions/.env.local and set your FIREBASE_PROJECT_ID"
else
    echo "✅ .env.local already exists"
fi

# Step 3: Build functions
echo ""
echo "🔨 Building functions..."
npm run build
echo "✅ Functions built successfully"

# Step 4: Go back to project root
cd ..

# Step 5: Check Firebase project
echo ""
echo "🔍 Checking Firebase project configuration..."
if firebase projects:list &> /dev/null; then
    echo "✅ Firebase project configured"
else
    echo "⚠️  Firebase project not configured. Run 'firebase login' and 'firebase use --add' if needed"
fi

echo ""
echo "🎉 Setup complete! You can now:"
echo ""
echo "1. Start all emulators:"
echo "   firebase emulators:start"
echo ""
echo "2. Start only functions emulator:"
echo "   firebase emulators:start --only functions"
echo ""
echo "3. Use functions shell for testing:"
echo "   cd functions && npm run shell"
echo ""
echo "4. Access Emulator UI at:"
echo "   http://localhost:4000"
echo ""
echo "📖 For detailed debugging instructions, see DEBUGGING_GUIDE.md"
echo ""

# Ask if user wants to start emulators now
read -p "🚀 Would you like to start the emulators now? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "Starting Firebase emulators..."
    firebase emulators:start
fi
