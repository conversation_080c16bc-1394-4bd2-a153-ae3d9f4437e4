# Firebase Functions Configuration Setup

This document explains how to configure Firebase Functions using Firebase config instead of environment variables.

## Overview

The Firebase Functions have been refactored to use Firebase's built-in configuration system (`functions.config()`) instead of `process.env` variables. This provides better security and easier management of configuration across different environments.

## Setup Process

### 1. Create Environment File

Copy the example environment file and fill in your values:

```bash
cp .env.example .env
```

Edit `.env` with your actual configuration values:

```bash

# Firebase Configuration
FIREBASE_PROJECT_ID=your-actual-project-id

# Telegram Configuration
TELEGRAM_BOT_TOKEN=your-actual-telegram-bot-token

# TON Configuration
TON_RPC_URL_MAINNET=https://your-ton-rpc-url
TON_MARKETPLACE_WALLET=UQCWcm7v_aEFlLEUmAYuryEfpepeRcFVSVIimfmCYmxbE7gI

# Optional configurations
TONCENTER_API_KEY=your-api-key-if-needed
FIREBASE_SERVICE_ACCOUNT_KEY={"type":"service_account",...}
```

### 2. Run Configuration Setup

Execute the setup script to transfer all environment variables to Firebase config:

```bash
npm run config:setup
```

Or run directly:

```bash
node setup-firebase-config.js
```

This script will:

- Read variables from your `.env` file
- Validate required configurations
- Set them in Firebase Functions config using `firebase functions:config:set`

### 3. Verify Configuration

Check that all configurations were set correctly:

```bash
firebase functions:config:get
```

### 4. Deploy Functions

Now you can deploy your functions:

```bash
npm run deploy
```

## Configuration Structure

The configuration is organized into the following sections:

### App Configuration

- `app.environment`: Runtime environment (development/production)
- `app.project_id`: Firebase project ID

### Telegram Configuration

- `telegram.bot_token`: Telegram bot token for authentication

### TON Configuration

- `ton.rpc_url_mainnet`: TON mainnet RPC URL
- `ton.rpc_url_testnet`: TON testnet RPC URL (optional)
- `ton.marketplace_wallet`: Marketplace wallet address
- `ton.network`: Network to use (mainnet/testnet)
- `ton.api_key`: TON Center API key (optional)

### Firebase Configuration

- `firebase.service_account_key`: Service account key JSON (optional)

## Code Changes

### New Configuration System

The functions now use a centralized configuration system in `src/config.ts`:

```typescript
import { getConfig, getTelegramBotToken, getMarketplaceWallet } from "./config";

// Get full config
const config = getConfig();

// Get specific values
const botToken = getTelegramBotToken();
const wallet = getMarketplaceWallet();
```

### Moved Functions

- `authenticateWithTelegram` has been moved to `src/telegram-auth-functions.ts`
- All configuration helpers are in `src/config.ts`

## Required Variables

The following variables are required and must be set:

- `FIREBASE_PROJECT_ID`
- `TELEGRAM_BOT_TOKEN`
- `TON_MARKETPLACE_WALLET`

## Optional Variables

The following variables have defaults and are optional:

- `TON_RPC_URL_MAINNET` (has hardcoded fallback)
- `TONCENTER_API_KEY`
- `FIREBASE_SERVICE_ACCOUNT_KEY`

## Troubleshooting

### Configuration Not Found

If you get "not configured" errors, ensure you've run the setup script and all required variables are in your `.env` file.

### Permission Errors

Make sure you're authenticated with Firebase CLI:

```bash
firebase login
firebase use your-project-id
```

### Build Errors

Ensure all dependencies are installed:

```bash
npm install
npm run build
```

## Security Notes

- Never commit your `.env` file to version control
- The `.env` file is already in `.gitignore`
- Firebase config is stored securely in Firebase and only accessible to your functions
- Use Firebase service account keys only when necessary for custom token creation
