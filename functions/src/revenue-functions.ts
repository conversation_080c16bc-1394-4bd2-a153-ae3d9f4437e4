import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import { TonClient, WalletContractV4, internal } from "@ton/ton";
import { mnemonicToPrivateKey } from "@ton/crypto";
import { UserEntity } from "./types";
import { hasAvailableBalance, spendLockedFunds } from "./balance-service";
import { getTonRpcUrl, getMarketplaceWalletMnemonic } from "./config";
import { MARKETPLACE_REVENUE_USER_ID } from "./constants";

interface WithdrawRevenueData {
  withdrawAmount: number;
  alexWallet: string;
  mikeWallet: string;
  pittWallet: string;
}

export const withdrawRevenue = functions.https.onCall(
  async (data: WithdrawRevenueData, context) => {
    // Validate authentication
    if (!context.auth) {
      throw new functions.https.HttpsError(
        "unauthenticated",
        "User must be authenticated to withdraw revenue."
      );
    }

    const { withdrawAmount, alexWallet, mikeWallet, pittWallet } = data;

    // Validate input
    if (!withdrawAmount || withdrawAmount <= 0) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Withdrawal amount must be greater than 0."
      );
    }

    if (!alexWallet || !mikeWallet || !pittWallet) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "All wallet addresses are required."
      );
    }

    try {
      const db = admin.firestore();
      const userId = context.auth.uid;

      // Check if user is admin
      const userDoc = await db.collection("users").doc(userId).get();
      if (!userDoc.exists) {
        throw new functions.https.HttpsError("not-found", "User not found.");
      }

      const user = { id: userDoc.id, ...userDoc.data() } as UserEntity;
      if (user.role !== "admin") {
        throw new functions.https.HttpsError(
          "permission-denied",
          "Only admin users can withdraw revenue."
        );
      }

      // Get marketplace revenue user
      const revenueDoc = await db
        .collection("users")
        .doc(MARKETPLACE_REVENUE_USER_ID)
        .get();

      if (!revenueDoc.exists) {
        throw new functions.https.HttpsError(
          "not-found",
          "Marketplace revenue account not found."
        );
      }

      const revenueUser = {
        id: revenueDoc.id,
        ...revenueDoc.data(),
      } as UserEntity;

      const availableRevenue = revenueUser.balance
        ? revenueUser.balance.sum - revenueUser.balance.locked
        : 0;

      // Validate minimum balance requirement (must keep 10 TON)
      if (availableRevenue < 10) {
        throw new functions.https.HttpsError(
          "failed-precondition",
          "Cannot withdraw when revenue balance is less than 10 TON."
        );
      }

      if (withdrawAmount > availableRevenue - 10) {
        throw new functions.https.HttpsError(
          "failed-precondition",
          `Cannot withdraw more than ${(availableRevenue - 10).toFixed(
            4
          )} TON. Must keep 10 TON minimum.`
        );
      }

      // Check if marketplace revenue has sufficient balance
      const hasBalance = await hasAvailableBalance(
        MARKETPLACE_REVENUE_USER_ID,
        withdrawAmount
      );
      if (!hasBalance) {
        throw new functions.https.HttpsError(
          "failed-precondition",
          "Insufficient revenue balance for withdrawal."
        );
      }

      // Calculate amount per person (divide by 3)
      const amountPerPerson = withdrawAmount / 3;

      // Deduct the full amount from marketplace revenue balance
      await spendLockedFunds(MARKETPLACE_REVENUE_USER_ID, withdrawAmount);

      // Initialize TON client
      const tonRpcUrl = getTonRpcUrl();
      const client = new TonClient({
        endpoint: tonRpcUrl,
      });

      // Get marketplace wallet
      const marketplaceWalletMnemonic = getMarketplaceWalletMnemonic();
      const keyPair = await mnemonicToPrivateKey(
        marketplaceWalletMnemonic.split(" ")
      );

      const marketplaceWallet = WalletContractV4.create({
        publicKey: keyPair.publicKey,
        workchain: 0,
      });

      const marketplaceContract = client.open(marketplaceWallet);

      // Get sequence number for the transaction
      const seqno = await marketplaceContract.getSeqno();

      // Convert amount to nanotons (1 TON = 1,000,000,000 nanotons)
      const amountInNanotons = Math.floor(amountPerPerson * 1000000000);

      // Create transfer transaction to all three wallets
      const transfer = marketplaceContract.createTransfer({
        seqno,
        secretKey: keyPair.secretKey,
        messages: [
          internal({
            value: amountInNanotons.toString(),
            to: alexWallet,
            body: "Revenue distribution - Alex",
          }),
          internal({
            value: amountInNanotons.toString(),
            to: mikeWallet,
            body: "Revenue distribution - Mike",
          }),
          internal({
            value: amountInNanotons.toString(),
            to: pittWallet,
            body: "Revenue distribution - Pitt",
          }),
        ],
      });

      // Send the transaction
      await marketplaceContract.send(transfer);

      console.log(
        `Revenue distributed: ${amountPerPerson.toFixed(
          4
        )} TON sent to each of 3 wallets`
      );

      return {
        success: true,
        message: `Successfully distributed ${withdrawAmount} TON revenue (${amountPerPerson.toFixed(
          4
        )} TON per person)`,
        amountPerPerson: amountPerPerson,
        totalAmount: withdrawAmount,
      };
    } catch (error) {
      console.error("Error in withdrawRevenue function:", error);
      if (error instanceof functions.https.HttpsError) {
        throw error;
      }
      throw new functions.https.HttpsError(
        "internal",
        "An error occurred while processing the revenue withdrawal."
      );
    }
  }
);
